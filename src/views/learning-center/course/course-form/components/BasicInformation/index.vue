<script setup lang="ts" name="Basic Information">
import type { FormInstance, FormRules } from 'element-plus'
import type { ComponentInternalInstance } from 'vue'
import { listTopic, listTopicAll } from '@/api/category/topic'
import { deepClone } from '@/utils'
import { addCourse, getCourse, updateCourse, getCourseTags } from '@/api/topicMgt/elearning'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { handlePhaseTree } from '@/utils/tree'
// 文件上传组件
import ImageUpload from '@/components/ImageUpload/index.vue'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import Generate from '/src/assets/svgs/generate.svg'
const props = defineProps<{
  courseId: string
}>()
interface FormData {
  id?: string
  name: string
  cover: string
  introduction: string
  skillTags: string[]
  topicId: string | number[] | undefined
  effectiveDay: number
  isAutoAssign: boolean
  keywords: string | []
  isRecommend: boolean
  isCertificateGenerated: boolean
  isNew: boolean
  isSubtitle: boolean
  subtitle: boolean
  language: string
  source: string
  handDuration: string // 手动课程时长
  exam: number
  level?: string | number
}
const hourNum = ref(0) // 小时
const minuteNum = ref(0) // 分钟
const secondNum = ref(0) // 秒
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { router, push, currentRoute } = useRouter() // 路由
const formData = ref<FormData>({
  name: '',
  cover: '',
  introduction: '',
  skillTags: [],
  topicId: [],
  effectiveDay: -1,
  isAutoAssign: false,
  keywords: [],
  isRecommend: false,
  isCertificateGenerated: false,
  isNew: false,
  level: 0,
  isSubtitle: false,
  language: '',
  source: '',
  handDuration: '',
  exam: 0,
  status: 1
})
const formRules = ref<FormRules<FormData>>({
  topicId: [
    {
      required: true,
      message: t('learningCenter.course.topicIdRule'),
      trigger: 'change'
    }
  ],
  name: [
    {
      required: true,
      message: t('learningCenter.course.courseNameRule'),
      trigger: 'change'
    }
  ],
  effectiveDay: [
    {
      required: false,
      message: t('common.inputText'),
      trigger: 'change'
    }
  ],
  cover: [
    {
      required: true,
      message: t('common.uploadText'),
      trigger: 'change'
    }
  ],
  language: [
    {
      required: true,
      message: t('common.selectText'),
      trigger: 'change'
    }
  ],
  source: [
    {
      required: true,
      message: t('common.selectText'),
      trigger: 'change'
    }
  ],
  handDuration: [
    {
      required: true,
      message: t('common.inputText'),
      trigger: 'change'
    }
  ]
})
const effectiveDayType = ref(-1)
const effectiveDayNum = ref(-1)
const topicOptions = ref<any[]>()
const subjectList = ref()
const keywordsOptions = ref<string[]>()
const loading = ref(false)
const formRef = ref<FormInstance>()
/** initStatus 初始化状态值，与Subject切换值时配合使用 */
const initStatus = ref(false)

const inputValue = ref('')
const inputVisible = ref()
const InputRef = ref()
const repeatKeyword = ref(false)

// 技能标签相关的响应式变量
const skillTagInputValue = ref('')
const skillTagInputRef = ref()

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value!.input!.focus()
  })
}
const handleInputConfirm = () => {
  if (formData.value.keywords.includes(inputValue.value)) {
    repeatKeyword.value = true
    return
  }
  repeatKeyword.value = false
  if (inputValue.value) {
    formData.value.keywords.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const handleCloseTag = (tag: string) => {
  formData.value.keywords.splice(formData.value.keywords.indexOf(tag), 1)
  if (formData.value.keywords.includes(inputValue.value)) {
    repeatKeyword.value = true
  } else {
    repeatKeyword.value = false
  }
}
const handleAddTag = (tag: string) => {
  inputValue.value = tag
  if (formData.value.keywords.length >= 5) {
    return
  }
  handleInputConfirm()
}

// 技能标签相关的处理函数
const handleSkillTagInputConfirm = () => {
  if (skillTagInputValue.value && !formData.value.skillTags.includes(skillTagInputValue.value)) {
    formData.value.skillTags.push(skillTagInputValue.value)
  }
  skillTagInputValue.value = ''
}

const handleCloseSkillTag = (tag: string) => {
  formData.value.skillTags.splice(formData.value.skillTags.indexOf(tag), 1)
}

// 点击容器时聚焦到输入框
const focusInput = () => {
  nextTick(() => {
    skillTagInputRef.value?.focus()
  })
}

// 生成按钮点击事件
const sendMessage = () => {
  // TODO: 实现生成逻辑
  console.log('Generate button clicked')
}

const getDetail = async () => {
  try {
    const data = await getCourse(props.courseId)
    if (data) {
      const resData = data as FormData
      // 特殊处理keywords参数
      const keywords = resData.keywords ? (resData.keywords as string).split(',') : undefined
      formData.value.topicId = resData.topicId?.split(',').map((item) => {
        return Number(item)
      })
      formData.value.keywords = keywords
      // 特殊处理effectiveDay参数
      if (resData.effectiveDay > 0) {
        effectiveDayType.value = 1
        effectiveDayNum.value = resData.effectiveDay
      } else if (resData.effectiveDay === -1) {
        effectiveDayType.value = -1
      }
      // 特殊处理keywordsoptions
      handleSubjectChange(formData.value.topicId, false)
      initStatus.value = true

      formData.value.cover = resData.cover
      formData.value.name = resData.name
      formData.value.introduction = resData.introduction
      formData.value.id = resData.id
      formData.value.isAutoAssign = resData.isAutoAssign
      formData.value.isRecommend = resData.isRecommend
      formData.value.exam = resData.exam
      formData.value.isNew = resData.isNew
      formData.value.isCertificateGenerated = resData.isCertificateGenerated
      formData.value.level = resData.level
      formData.value.isSubtitle = resData.subtitle
      formData.value.language = resData.language
      formData.value.source = resData.source
      formData.value.handDuration = secondsToTime(Number(resData.handDuration))
      /**
       * 解决：
       * 1、添加keywords时，报push is not function的问题 keywords不能为空；
       * 2、编辑返回的keywords没有值，页面上不显示Key Words的问题。
       */
      if (!formData.value.keywords) {
        formData.value.keywords = []
      }
    }
  } finally {
  }
}

const secondsToTime = (totalSeconds: number) => {
  // 计算小时、分钟和秒
  const hours = Math.floor(totalSeconds / 3600) // 计算小时数
  const minutes = Math.floor((totalSeconds % 3600) / 60) // 计算分钟数
  const seconds = totalSeconds % 60 // 计算剩余秒数

  hourNum.value = hours
  minuteNum.value = minutes
  secondNum.value = seconds
  // 格式化为 02:43:11 格式，补充零
  // const formattedTime = `${padZero(hours)}:${padZero(minutes)}:${padZero(seconds)}`;

  // return formattedTime
}

// 补零函数，确保时间格式为两位
const padZero = (num: string | number) => {
  return num < 10 ? `0${num}` : `${num}`
}

const getTopicAll = async () => {
  topicOptions.value = await listTopicAll()
  if (props.courseId) {
    await getDetail()
  }
}

const getCourseTags = async () => {
  try {
    const data = await getCourseTags()
    console.log('生成课程标签', data)

    formData.value.skillTags = data.tags
  } catch (error) {
    console.log('生成课程标签失败', error)
  }
}

/**
 *
 * @param id topicid，用于读取keywords列表
 * @param clear 是否清空当前选择的keywords
 */
const handleSubjectChange = (id: string, clear = true) => {
  const findKeywords = ref()
  /**
   * 判断id的值是否为数组
   * 重新选择subject时，值为数组
   * 编辑回显时，值为数值
   */
  if (Array.isArray(id)) {
    findKeywords.value = topicOptions.value?.find((item) => item.id === id[id.length - 1])
      ?.keywords as string
  } else {
    findKeywords.value = topicOptions.value?.find((item) => item.id === id)?.keywords as string
  }
  if (findKeywords.value) {
    keywordsOptions.value = deepClone(findKeywords.value.split(','))
    if (findKeywords.value.includes(keywordsOptions.value)) {
      if (initStatus.value) {
        formData.value.keywords = [] // 切换subject时，清空上一个subject的keywords数据
      }
    }
  } else if (clear) {
    if (findKeywords.value == null) {
      formData.value.keywords = []
      keywordsOptions.value = []
    } else {
      formData.value.keywords = []
      keywordsOptions.value = []
    }
  }
}

const handleConfirm = () => {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const subjectIds = formData.value.topicId
      // 特殊处理keywords参数
      const keywords = formData.value.keywords
        ? (formData.value.keywords as string[]).join(',')
        : undefined
      // keywords有重复的，提交时，去掉重复值
      if (repeatKeyword.value) {
        inputVisible.value = false
        repeatKeyword.value = false
        inputValue.value = ''
      }
      // 特殊处理effectiveDay
      if (effectiveDayType.value === -1) {
        formData.value.effectiveDay = -1
      } else if (effectiveDayType.value === 1) {
        formData.value.effectiveDay = effectiveDayNum.value
      }
      // if (Array.isArray(formData.value.topicId)) {
      //   formData.value.topicId = formData.value.topicId[formData.value.topicId.length - 1]
      // }
      formData.value.topicId = formData.value.topicId.join(',')
      const dataParams = {
        ...formData.value,
        keywords
      }
      formData.value.topicId = subjectIds
      dataParams.handDuration = hourNum.value * 3600 + minuteNum.value * 60 + secondNum.value
      if (dataParams.handDuration === 0) {
        message.error(t('error.pleaseSetDuration'))
        loading.value = false
        return
      }
      if (props.courseId) {
        try {
          await updateCourse(dataParams)
          message.success(t('common.updateSuccess'))
          await getDetail()
        } finally {
          loading.value = false
        }
      } else {
        try {
          const data = await addCourse(dataParams)
          message.success(t('common.createSuccess'))
          push(`/course-form/index/${data}?active=2`)
        } finally {
          loading.value = false
        }
      }
    } else {
      loading.value = false
    }
  })
}

const handleClose = () => {
  delView(unref(currentRoute))
  push('/learning-center/course')
}
const getList = async () => {
  const data = await listTopic({})
  data.forEach((element) => {
    element.value = element.id
    element.label = element.name
  })

  subjectList.value = handlePhaseTree(data, 'id')
}
onMounted(() => {
  getList()
  getCourseTags()
  getTopicAll()
})
</script>

<template>
  <ContentWrap>
    <div class="rounded-x py-5 px-7">
      <span class="text-[#222222] text-xl"> {{ t('learningCenter.course.basicInfo') }} </span>
      <!-- 表单区域 -->
      <el-form
        ref="formRef"
        :model="formData"
        label-width="auto"
        label-position="left"
        class="mt-5 w-3/4"
        :rules="formRules"
      >
        <el-form-item :label="t('learningCenter.course.courseName')" prop="name">
          <el-input v-model="formData.name" show-word-limit maxlength="200" />
        </el-form-item>
        <el-form-item prop="cover" :label="t('learningCenter.journey.cover')">
          <ImageUpload
            v-model="formData.cover"
            :tip-text="t('learningCenter.course.coverPH')"
            :limit="1"
            :is-show-tip="true"
            :file-size="5"
          />
        </el-form-item>
        <el-form-item prop="topicId" :label="t('category.topic.subjectName')">
          <el-tree-select
            v-model="formData.topicId"
            :data="subjectList"
            multiple
            :render-after-expand="false"
            show-checkbox
            check-strictly
            check-on-click-node
          />
        </el-form-item>
        <el-form-item prop="keywords" :label="t('category.topic.keyWords')">
          <div>
            <div class="flex flex-wrap gap-2 w-full">
              <el-tag
                v-for="tag in formData.keywords"
                :key="tag"
                closable
                :disable-transitions="false"
                @close="handleCloseTag(tag)"
              >
                {{ tag }}
              </el-tag>
              <el-input
                v-if="inputVisible"
                ref="InputRef"
                v-model="inputValue"
                class="!w-60"
                size="small"
                maxlength="300"
                show-word-limit
                @keyup.enter="handleInputConfirm"
                @blur="handleInputConfirm"
              />
              <el-button
                v-else-if="!inputVisible && formData.keywords && formData.keywords.length < 5"
                class="button-new-tag"
                size="small"
                @click="showInput"
              >
                {{ t('action.addKeyWord') }}
              </el-button>
            </div>
            <div v-if="repeatKeyword" class="text-[#f56c6c] text-xs my-2">
              {{ t('common.keyWords') }}
            </div>
            <div class="text-[#ABACAE] text-xs my-2">
              {{ t('common.keyWordsLength') }}
            </div>
          </div>
          <div class="w-full flex flex-wrap gap-2">
            <div v-if="keywordsOptions" class="text-[#ABACAE] text-xs">
              {{ t('common.isRecommend') }}
            </div>

            <div
              v-for="tag in keywordsOptions"
              :key="tag"
              type="info"
              effect="plain"
              @click="handleAddTag(tag)"
            >
              <div v-if="formData.keywords">
                <div
                  :class="[
                    formData.keywords.indexOf(tag) >= 0 ? 'custom-tagdiv-active' : 'custom-tagdiv'
                  ]"
                >
                  {{ tag }}
                </div>
              </div>
            </div>
          </div>
          <el-select
            v-model="formData.keywords"
            name="topicId"
            filterable
            clearable
            :placeholder="t('common.selectText')"
            class="w-[400px] hidden"
            multiple
          >
            <el-option v-for="item in keywordsOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item
          v-show="false"
          :label="t('learningCenter.course.timeliness')"
          prop="effectiveDay"
        >
          <el-radio-group
            id="effectiveDay"
            v-model="effectiveDayType"
            class="flex-col !items-start"
          >
            <el-radio :value="-1">
              {{ t('learningCenter.course.permanent') }}
            </el-radio>
            <el-radio :value="1">
              {{ t('learningCenter.course.setExpirationDate') }}
            </el-radio>
          </el-radio-group>
          <div class="flex items-end h-full">
            <el-input-number
              v-model="effectiveDayNum"
              :disabled="effectiveDayType === -1"
              class="ml-4"
              :min="1"
              :max="10"
              aria-labe
            />&nbsp;{{ t('learningCenter.course.days') }}
          </div>
        </el-form-item>
        <el-form-item prop="isSubtitle" label="isSubtitle">
          <el-radio-group id="isSubtitle" v-model="formData.isSubtitle" name="isSubtitle	">
            <el-radio name="assignTrue" :value="true">
              {{ t('common.yes') }}
            </el-radio>
            <el-radio name="assignFalse" :value="false">
              {{ t('common.no') }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="isAutoAssign" :label="t('learningCenter.course.autoAssign')">
          <el-radio-group id="isAutoAssign" v-model="formData.isAutoAssign" name="isAutoAssign">
            <el-radio name="assignTrue" :value="true">
              {{ t('common.yes') }}
            </el-radio>
            <el-radio name="assignFalse" :value="false">
              {{ t('common.no') }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="isCertificateGenerated" :label="t('learningCenter.course.certificate')">
          <el-radio-group v-model="formData.isCertificateGenerated" name="certificate">
            <el-radio name="assignTrue" :value="true">
              {{ t('common.yes') }}
            </el-radio>
            <el-radio name="assignFalse" :value="false">
              {{ t('common.no') }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="isNew" :label="t('learningCenter.course.newCourse')">
          <el-radio-group v-model="formData.isNew" name="isNew">
            <el-radio name="isNewTrue" :value="true">
              {{ t('common.yes') }}
            </el-radio>
            <el-radio name="isNewFalse" :value="false">
              {{ t('common.no') }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          id="isRecommend"
          prop="isRecommend"
          :label="t('learningCenter.course.isRecommend')"
        >
          <el-radio-group v-model="formData.isRecommend" name="recommendation">
            <el-radio name="isRecommendTrue" :value="true">
              {{ t('common.yes') }}
            </el-radio>
            <el-radio name="isRecommendFalse" :value="false">
              {{ t('common.no') }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="exam" :label="t('learningCenter.course.exam')">
          <el-radio-group id="exam" v-model="formData.exam" name="exam">
            <el-radio name="examTrue" :value="1">
              {{ t('common.yes') }}
            </el-radio>
            <el-radio name="examFalse" :value="0">
              {{ t('common.no') }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="language" :label="t('learningCenter.course.language')">
          <el-select v-model="formData.language" clearable filterable>
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.RESOURCE_LIST_LANG)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="level" :label="t('learningCenter.course.level')">
          <el-select v-model="formData.level" clearable filterable>
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_LEVEL)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="source" :label="t('learningCenter.course.courseSource')">
          <el-select v-model="formData.source" clearable filterable>
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_SOURCE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('learningCenter.course.duration')">
          <!--        <el-time-picker-->
          <!--          v-model="formData.handDuration"-->
          <!--          format="HH:mm:ss"-->
          <!--          :picker-options="pickerOptions"-->
          <!--          value-format="HH:mm:ss"-->
          <!--          placeholder="Duration"-->
          <!--        />-->
          <el-input-number v-model="hourNum" :min="0" :max="999" :controls="false" />&nbsp;h&nbsp;
          <el-input-number v-model="minuteNum" :min="0" :max="59" :controls="false" />&nbsp;m&nbsp;
          <el-input-number v-model="secondNum" :min="0" :max="59" :controls="false" />&nbsp;s
        </el-form-item>
        <el-form-item prop="introduction" :label="t('learningCenter.course.courseDescription')">
          <div class="relative">
            <el-input
              v-model="formData.introduction"
              type="textarea"
              :rows="5"
              show-word-limit
              maxlength="5000"
            />
            <!--生成按钮-->
            <el-button text class="!w-8 absolute top-1 right-1 z-10" @click="() => sendMessage()">
              <img :src="Generate" alt="Generate" />
            </el-button>
          </div>
        </el-form-item>

        <!-- 技能标签 -->
        <el-form-item prop="skillTags" label="Skill Tags">
          <div
            class="flex flex-wrap gap-2 w-full p-2 border border-[#dedfe6] border-solid rounded min-h-[40px] skill-tags-container"
            @click="focusInput"
          >
            <template v-for="tag in formData.skillTags" :key="tag">
              <el-tag closable :disable-transitions="false" @close="handleCloseSkillTag(tag)">
                {{ tag }}
              </el-tag>
            </template>

            <el-input
              ref="skillTagInputRef"
              v-model="skillTagInputValue"
              class="!w-60 skill-tag-input"
              size="small"
              maxlength="50"
              placeholder="Enter the skill tag and press Enter"
              @keyup.enter="handleSkillTagInputConfirm"
              @blur="handleSkillTagInputConfirm"
            />
          </div>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item label=" ">
          <el-button type="primary" :loading="loading" @click="handleConfirm">
            {{ t('global.confirm') }}
          </el-button>
          <el-button type="primary" plain @click="handleClose">
            {{ t('global.cancel') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </ContentWrap>
</template>

<style scoped lang="scss">
:deep .custom-tagdiv {
  border: 1px solid #c8c9cc;
  color: #909399;
  line-height: 1.9;
  border-radius: 4px;
  padding: 5px 5px;
  cursor: pointer;
  font-size: 12px !important;
  justify-content: center;
  align-items: center;
  vertical-align: middle;
  height: 24px;
  padding: 0 9px;
}
:deep .custom-tagdiv-active {
  border: 1px solid #007943;
  color: #007943;
  line-height: 1.9;
  border-radius: 4px;
  padding: 5px 5px;
  cursor: pointer;
  font-size: 12px !important;
  justify-content: center;
  align-items: center;
  vertical-align: middle;
  height: 24px;
  padding: 0 9px;
}
:deep .custom-tagdiv:hover {
  border: 1px solid #007943;
  color: #007943;
}

:deep(.el-input-number) {
  width: 50px;
}
:deep(.el-input-number.is-without-controls .el-input__wrapper) {
  padding-left: 11px;
  padding-right: 11px;
}

/* 技能标签输入框样式 - 去掉边框和hover效果 */
:deep(.skill-tag-input .el-input__wrapper) {
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
  padding: 0 !important;
}

:deep(.skill-tag-input .el-input__wrapper:hover) {
  border: none !important;
  box-shadow: none !important;
}

:deep(.skill-tag-input .el-input__wrapper:focus-within) {
  border: none !important;
  box-shadow: none !important;
}

:deep(.skill-tag-input .el-input__inner) {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

:deep(.skill-tag-input .el-input__inner:focus) {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 技能标签容器样式 - 模拟el-input的hover和active效果 */
.skill-tags-container {
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  cursor: text;
}

.skill-tags-container:hover {
  border-color: var(--el-border-color-hover) !important;
}

.skill-tags-container:focus-within {
  border-color: var(--el-color-primary) !important;
  box-shadow: var(--el-input-fucus-border-color) inset !important;
}

/* 当容器内的输入框获得焦点时的样式 */
.skill-tags-container:has(.skill-tag-input .el-input__inner:focus) {
  border-color: var(--el-color-primary) !important;
  box-shadow: var(--el-input-fucus-border-color) inset !important;
}
</style>
